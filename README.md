# ShipAny Template One

🚀 Ship Any AI SaaS Startups in hours, not days.

![preview](preview.png)

## 🎯 Overview

ShipAny Template One is a modern, production-ready AI SaaS boilerplate built with **Next.js 15** and **React 19**. It provides everything you need to launch an AI-powered SaaS product quickly, including user authentication, payment processing, AI integrations, and a complete admin dashboard.

### ✨ Key Features

- 🤖 **Multi-AI Provider Support** - OpenAI, DeepSeek, OpenRouter, SiliconFlow
- 💰 **Complete Payment System** - Stripe integration with subscriptions
- 🔐 **Modern Authentication** - NextAuth.js 5.0 with multiple providers
- 🌍 **Internationalization** - Built-in i18n support (English/Chinese)
- 📊 **Admin Dashboard** - User management, analytics, and system monitoring
- 💳 **Credit System** - Usage-based billing and quota management
- 🎨 **Modern UI** - shadcn/ui components with dark/light themes
- 📱 **Responsive Design** - Mobile-first approach
- 🗄️ **Supabase Backend** - PostgreSQL database with real-time features

### 🛠️ Tech Stack

- **Frontend**: Next.js 15, React 19, TypeScript, Tailwind CSS
- **Backend**: Next.js API Routes, Supabase
- **Authentication**: NextAuth.js 5.0
- **Payment**: Stripe
- **AI**: Vercel AI SDK with multiple providers
- **Database**: Supabase (PostgreSQL)
- **Deployment**: Vercel, Cloudflare Pages, Docker

## 🚀 Quick Start

### 1. Clone the repository

```bash
git clone https://github.com/shipanyai/shipany-template-one.git
cd shipany-template-one
```

### 2. Install dependencies

```bash
pnpm install
```

### 3. Set up environment variables

```bash
cp .env.example .env.local
```

Edit `.env.local` with your configuration:

```env
# Database
SUPABASE_URL=your_supabase_url
SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key

# Authentication
NEXTAUTH_SECRET=your_nextauth_secret
NEXTAUTH_URL=http://localhost:3000

# AI Providers
OPENAI_API_KEY=your_openai_key
DEEPSEEK_API_KEY=your_deepseek_key
OPENROUTER_API_KEY=your_openrouter_key

# Payment
STRIPE_SECRET_KEY=your_stripe_secret_key
STRIPE_PUBLISHABLE_KEY=your_stripe_publishable_key
```

### 4. Set up the database

```bash
# Run the SQL script in your Supabase dashboard
cat data/install.sql
```

### 5. Run the development server

```bash
pnpm dev
```

Open [http://localhost:3000](http://localhost:3000) to see your application.

## 📁 Project Structure

```
├── aisdk/                 # AI SDK integrations
│   ├── generate-video/    # Video generation (Kling AI)
│   ├── kling/            # Kling AI provider
│   └── provider/         # Custom AI providers
├── app/                  # Next.js app directory
│   ├── [locale]/         # Internationalized routes
│   ├── api/              # API routes
│   └── globals.css       # Global styles
├── auth/                 # Authentication configuration
├── components/           # Reusable UI components
│   ├── ui/               # shadcn/ui components
│   ├── dashboard/        # Dashboard components
│   └── console/          # User console components
├── contexts/             # React contexts
├── hooks/                # Custom React hooks
├── i18n/                 # Internationalization
│   ├── messages/         # Translation files
│   └── pages/            # Page content translations
├── lib/                  # Utility functions
├── models/               # Database models
├── services/             # Business logic services
├── types/                # TypeScript type definitions
└── public/               # Static assets
```

## 🎨 Customization

### Theme Customization

1. **Update your theme colors** in `app/theme.css`

   Use the [shadcn-ui-theme-generator](https://zippystarter.com/tools/shadcn-ui-theme-generator) for easy color palette generation.

2. **Customize landing page content** in `i18n/pages/landing/`

3. **Update translations** in `i18n/messages/en.json` and `i18n/messages/zh.json`

### Adding New AI Providers

1. Create a new provider in `aisdk/provider/`
2. Add configuration in your API routes
3. Update the frontend components to include the new provider

### Database Schema

The project uses Supabase with the following main tables:
- `users` - User profiles and settings
- `credits` - User credit transactions
- `orders` - Payment and subscription records
- `api_keys` - User API key management
- `feedback` - User feedback and support

## 🚀 Deployment

### Deploy to Vercel (Recommended)

The easiest way to deploy your ShipAny application:

[![Deploy with Vercel](https://vercel.com/button)](https://vercel.com/new/clone?repository-url=https%3A%2F%2Fgithub.com%2Fshipanyai%2Fshipany-template-one&project-name=my-shipany-project&repository-name=my-shipany-project&redirect-url=https%3A%2F%2Fshipany.ai&demo-title=ShipAny&demo-description=Ship%20Any%20AI%20Startup%20in%20hours%2C%20not%20days&demo-url=https%3A%2F%2Fshipany.ai&demo-image=https%3A%2F%2Fpbs.twimg.com%2Fmedia%2FGgGSW3La8AAGJgU%3Fformat%3Djpg%26name%3Dlarge)

1. Click the deploy button above
2. Connect your GitHub account
3. Configure environment variables in Vercel dashboard
4. Deploy automatically

### Deploy to Cloudflare Pages

For edge deployment with global performance:

1. **Prepare configuration files**

```bash
cp .env.example .env.production
cp wrangler.toml.example wrangler.toml
```

2. **Configure environment variables**

Edit `.env.production` with your production values, then add all environment variables under `[vars]` in `wrangler.toml`:

```toml
[vars]
SUPABASE_URL = "your_supabase_url"
SUPABASE_ANON_KEY = "your_supabase_anon_key"
# ... add all your environment variables
```

3. **Deploy to Cloudflare**

```bash
pnpm cf:deploy
```

### Docker Deployment

For containerized deployment:

```bash
# Build the Docker image
pnpm docker:build

# Run the container
docker run -p 3000:3000 --env-file .env.local shipany-template-one:latest
```

## 🔧 Development Scripts

```bash
# Development
pnpm dev              # Start development server with Turbopack
pnpm build            # Build for production
pnpm start            # Start production server
pnpm lint             # Run ESLint

# Analysis & Optimization
pnpm analyze          # Analyze bundle size

# Cloudflare
pnpm cf:build         # Build for Cloudflare Pages
pnpm cf:preview       # Preview Cloudflare deployment locally
pnpm cf:deploy        # Deploy to Cloudflare Pages

# Docker
pnpm docker:build     # Build Docker image
```

## 🎯 Use Cases

This template is perfect for building:

- 🤖 **AI Text Generation Tools** - ChatGPT-like applications
- 🎨 **AI Image/Video Generators** - DALL-E, Midjourney alternatives
- 💬 **AI Chatbot Platforms** - Customer service, education bots
- 🔄 **Multi-modal AI Apps** - Text-to-image, image-to-text tools
- 💼 **B2B AI SaaS** - API-first AI services with usage billing
- 🎓 **AI Education Platforms** - Learning tools with AI tutors

## 🤝 Contributing

We welcome contributions! Please see our contributing guidelines:

1. Fork the repository
2. Create a feature branch: `git checkout -b feature/amazing-feature`
3. Commit your changes: `git commit -m 'Add amazing feature'`
4. Push to the branch: `git push origin feature/amazing-feature`
5. Open a Pull Request

## 📚 Resources & Community

- 🌐 **Website**: [ShipAny.ai](https://shipany.ai)
- 📖 **Documentation**: [docs.shipany.ai](https://docs.shipany.ai)
- 💬 **Discord Community**: [Join our Discord](https://discord.gg/HQNnrzjZQS)
- 🐛 **Issues**: [GitHub Issues](https://github.com/shipanyai/shipany-template-one/issues)
- 💡 **Feature Requests**: [GitHub Discussions](https://github.com/shipanyai/shipany-template-one/discussions)

## 📄 License

This project is licensed under the [ShipAny AI SaaS Boilerplate License Agreement](LICENSE).

---

<div align="center">
  <p>Built with ❤️ by the <a href="https://shipany.ai">ShipAny</a> team</p>
  <p>⭐ Star us on GitHub if this project helped you!</p>
</div>
